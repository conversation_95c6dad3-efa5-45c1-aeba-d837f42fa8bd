load "rogueutil.ring"

# Snake Game Constants
C_GAME_WIDTH = 40
C_GAME_HEIGHT = 20
C_SNAKE_CHAR = "█"
C_FOOD_CHAR = "●"
C_WALL_CHAR = "▓"

# Game Variables
aSnake = [[10, 10]]  # Snake body positions
nFoodX = 15
nFoodY = 15
nDirection = KEY_RIGHT
nScore = 0
bGameOver = false

# Initialize Game
func initGame
    hideCursor()
    setConsoleTitle("لعبة الدودة - النقاط: " + nScore)
    setColor(GREEN)
    setBackgroundColor(BLACK)
    cls()
    drawBorders()
    spawnFood()
    # Show instructions
    setColor(WHITE)
    printXY(C_GAME_WIDTH + 5, 3, "استخدم الأسهم للحركة")
    printXY(C_GAME_WIDTH + 5, 4, "ESC للخروج")
    printXY(C_GAME_WIDTH + 5, 6, "النقاط: " + nScore)

# Draw game borders
func drawBorders
    setColor(YELLOW)
    for nX = 1 to C_GAME_WIDTH + 2
        printXY(nX, 1, C_WALL_CHAR)
        printXY(nX, C_GAME_HEIGHT + 2, C_WALL_CHAR)
    next
    for nY = 1 to C_GAME_HEIGHT + 2
        printXY(1, nY, C_WALL_CHAR)
        printXY(C_GAME_WIDTH + 2, nY, C_WALL_CHAR)
    next

# Spawn food at random position
func spawnFood
    # Make sure food doesn't spawn on snake
    while true
        nFoodX = random(C_GAME_WIDTH - 2) + 2
        nFoodY = random(C_GAME_HEIGHT - 2) + 2
        bOnSnake = false
        for nI = 1 to len(aSnake)
            if aSnake[nI][1] = nFoodX and aSnake[nI][2] = nFoodY
                bOnSnake = true
                exit
            ok
        next
        if not bOnSnake
            exit
        ok
    end
    setColor(RED)
    printXY(nFoodX, nFoodY, C_FOOD_CHAR)

# Draw snake
func drawSnake
    setColor(GREEN)
    for nI = 1 to len(aSnake)
        printXY(aSnake[nI][1], aSnake[nI][2], C_SNAKE_CHAR)
    next

# Move snake
func moveSnake
    aHead = aSnake[1]
    aNewHead = [aHead[1], aHead[2]]
    
    switch nDirection
    on KEY_UP
        aNewHead[2] -= 1
    on KEY_DOWN  
        aNewHead[2] += 1
    on KEY_LEFT
        aNewHead[1] -= 1
    on KEY_RIGHT
        aNewHead[1] += 1
    off
    
    # Check collision with walls
    if aNewHead[1] <= 1 or aNewHead[1] >= C_GAME_WIDTH + 2 or
       aNewHead[2] <= 1 or aNewHead[2] >= C_GAME_HEIGHT + 2
        bGameOver = true
        return
    ok
    
    # Check collision with self
    for nI = 1 to len(aSnake)
        if aNewHead[1] = aSnake[nI][1] and aNewHead[2] = aSnake[nI][2]
            bGameOver = true
            return
        ok
    next
    
    # Add new head
    insert(aSnake, 1, aNewHead)
    
    # Check if food eaten
    if aNewHead[1] = nFoodX and aNewHead[2] = nFoodY
        nScore += 10
        setConsoleTitle("لعبة الدودة - النقاط: " + nScore)
        spawnFood()
    else
        # Remove tail
        aTail = aSnake[len(aSnake)]
        del(aSnake, len(aSnake))
        printXY(aTail[1], aTail[2], " ")
    ok

# Main game loop
func playGame
    initGame()
    
    while not bGameOver
        drawSnake()
        
        if kbhit()
            nKey = getKey()
            if nKey = KEY_ESCAPE
                bGameOver = true
            but nKey = KEY_UP and nDirection != KEY_DOWN
                nDirection = KEY_UP
            but nKey = KEY_DOWN and nDirection != KEY_UP
                nDirection = KEY_DOWN
            but nKey = KEY_LEFT and nDirection != KEY_RIGHT
                nDirection = KEY_LEFT
            but nKey = KEY_RIGHT and nDirection != KEY_LEFT
                nDirection = KEY_RIGHT
            ok
        ok
        
        moveSnake()
        msleep(150)
    end
    
    gameOver()

# Game over screen
func gameOver
    setColor(RED)
    printXY(C_GAME_WIDTH/2 - 5, C_GAME_HEIGHT/2, "انتهت اللعبة!")
    printXY(C_GAME_WIDTH/2 - 8, C_GAME_HEIGHT/2 + 1, "النقاط النهائية: " + nScore)
    printXY(C_GAME_WIDTH/2 - 10, C_GAME_HEIGHT/2 + 3, "اضغط أي مفتاح للخروج")
    getch()
    showCursor()
    resetColor()
    cls()


